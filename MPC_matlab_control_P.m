%% Model Predictive Control for Steel Strip Exit Temperature in Continuous Annealing Furnace Based on MATLAB MPC Toolbox

clear;clc;close all
%% 1. Load State-Space Model
% Define model path
model_path = 'models/ss_ab_model_0.9'; % Update to your model path

% Load A matrix
A = csvread(fullfile(model_path, 'A_matrix.csv'));
% Load B matrix
B = csvread(fullfile(model_path, 'B_matrix.csv'));
% Load C matrix
C = csvread(fullfile(model_path, 'C_matrix.csv'));

% Load model information
fid = fopen(fullfile(model_path, 'model_info.json'), 'r');
raw = fread(fid, inf);
str = char(raw');
fclose(fid);
model_info = jsondecode(str);

% Extract variable information
state_cols = model_info.state_variables;
input_cols = model_info.input_variables;
output_col = model_info.output_variables{1};

disp(['Model loaded successfully, source: ', model_path]);
disp('Model structure:');
disp(['  - State variables: ', num2str(length(state_cols))]);
disp(['  - Input variables: ', num2str(length(input_cols))]);
disp(['  - Output variables: ', num2str(length(model_info.output_variables))]);

% Build discrete state-space model (already discrete)
Ts = 5; % Sampling time
plant = ss(A, B, C, zeros(size(C,1), size(B,2)), Ts);

%% 2. Load Data
% Define database filename and .mat filename
db_filename = './webApi/data/measure_20250601.sqlite';
[~, name, ~] = fileparts(db_filename);  % Get database filename (without extension)
mat_filename = ['./data/' name '.mat'];  % Path and filename for saved data (.mat format)

% Check if file already exists
if exist(mat_filename, 'file')
    % If file exists, load the data
    disp('Loading existing measures_df data...');
    load(mat_filename, 'measures_df');
else
    % If file doesn't exist, load data from database
    disp('Loading data from database...');
    conn = sqlite(db_filename);
    
    % Build SQL query
    sql_query = ['SELECT speed, measureEntryTemp, measureExitTemp, setpointExitTemp, ' ...
                 'thick1, width1, weldPercent1, thick2, width2, weldPercent2, ' ...
                 'hd1, hd2, hd3, hd4, hd5, hd6, hd7, hd8, hd9, hd10, ' ...
                 'hd11, hd12, hd13, hd14, hd15, hd16, hd17, hd18, hd19, hd20, ' ...
                 'hd21, hd22, hd23, hd24, hd25, ' ...
                 'tt1, tt2, tt3, tt4, tt5, tt6, tt7, tt8, tt9, tt10, ' ...
                 'tt11, tt12, tt13, tt14, tt15, tt16, tt17, tt18, tt19, tt20, ' ...
                 'tt21, tt22, tt23, tt24, tt25 ' ...
                 'FROM measures'];
             
    % Execute query and retrieve data
    measures_df = fetch(conn, sql_query);
    
    % Close database connection
    close(conn);
    
    % Calculate average power
    avg_power = ((measures_df.hd1 + measures_df.hd25) * 700 + ...
                (measures_df.hd9 + measures_df.hd23) * 13 * 165 + ...
                (measures_df.hd2 + measures_df.hd3 + measures_df.hd4 + measures_df.hd5 + ...
                 measures_df.hd6 + measures_df.hd7 + measures_df.hd8 + measures_df.hd10 + ...
                 measures_df.hd11 + measures_df.hd12 + measures_df.hd13 + measures_df.hd14 + ...
                 measures_df.hd15 + measures_df.hd16 + measures_df.hd17 + measures_df.hd18 + ...
                 measures_df.hd19 + measures_df.hd20 + measures_df.hd21 + measures_df.hd22 + ...
                 measures_df.hd24) * 14 * 165) / 54200;

    % Add average power to measures_df
    measures_df.avg_power = avg_power;

    % Convert thickness from meters to micrometers
    measures_df.thick1 = measures_df.thick1 * 1000;
    measures_df.thick2 = measures_df.thick2 * 1000;

    % Remove NaN values
    rows_before = height(measures_df);
    measures_df = rmmissing(measures_df);
    rows_after = height(measures_df);
    disp(['Original data rows: ', num2str(rows_before)]);
    disp(['Data rows after cleaning: ', num2str(rows_after)]);
    disp(['Removed ', num2str(rows_before - rows_after), ' rows containing NaN values']);

    % Remove rows with zero speed
    zero_speed_rows = sum(measures_df.speed == 0);
    measures_df = measures_df(measures_df.speed > 0, :);
    disp(['Removed ', num2str(zero_speed_rows), ' rows with zero speed']);
    disp(['Final data rows: ', num2str(height(measures_df))]);

    % Save measures_df to .mat file
    save(mat_filename, 'measures_df');
end

%% 3. Prepare MPC Controller
PredictionHorizon = 60;
ControlHorizon = 30;

start = 1;
time_step = min(2000, height(measures_df));  % Simulation steps (limited by available data)

% Extract data needed for MPC
df = measures_df(start:start+time_step+PredictionHorizon, :);  % Include extra N_p points for reference

% Extract state, input, and reference data
% For X_data, select all state_cols variables
X_data = zeros(height(df), length(state_cols));
for i = 1:length(state_cols)
    X_data(:, i) = df.(state_cols{i});
end

% For U_data, select all input_cols variables
U_data = zeros(height(df), length(input_cols));
for i = 1:length(input_cols)
    U_data(:, i) = df.(input_cols{i});
end

% Output and reference
Y_data = df.measureExitTemp;
Y_ref = df.setpointExitTemp;

% Control constraints
u_min = 0;  % Minimum power = 0%, minimum speed
u_max = 100;  % Maximum power = 100%, maximum speed

du_min = -10;  % Minimum control rate of change
du_max = 10;   % Maximum control rate of change

% Output constraints
y_min = min(Y_ref) - 5;  % Allow 5 degrees deviation below reference temperature
y_max = max(Y_ref) + 5;  % Allow 5 degrees deviation above reference temperature

% Build MATLAB MPC controller
% Use only two control inputs: power and speed, other variables as measurable disturbances
nu = 1;   % Number of control inputs (power)
ny = 1;   % Number of outputs (exit temperature)
nx = size(A, 1);  % Number of states
nd = size(B, 2) - nu;  % Number of measured disturbances (other inputs)

% Create prediction model with measured disturbances
% Split B matrix into control input part and disturbance input part
Bu = B(:, 1:nu);
Bd = B(:, nu+1:end);

% Set MPC weights
W = struct();
W.ManipulatedVariables = 0.01 * ones(1,nu);      % Weight for control inputs
W.ManipulatedVariablesRate = 1 * ones(1,nu);     % Weight for control input rate of change
W.OutputVariables = 100;                         % Weight for output variables

% Create MPC constraints
% Use correct mpcInputConstraint and mpcOutputConstraint functions
MV = struct('Min', u_min, 'Max', u_max, 'RateMin', du_min, 'RateMax', du_max);

% Create output variable (OV) constraints
OV = struct('Min', y_min, 'Max', y_max);

try

    % Clear potentially existing old MPC objects
    clear mpcobj
    
    % Create new state-space model including disturbances
    plant_mpc = ss(A, [Bu Bd], C, zeros(ny, nu+nd), Ts);
    
    % Set MPC signal types
    plant_mpc = setmpcsignals(plant_mpc, 'MV', 1:nu, 'MD', nu+1:nu+nd);
    
    % Create MPC object
    mpcobj = mpc(plant_mpc, Ts, PredictionHorizon, ControlHorizon, W, MV, OV);
        
    % Display MPC object information
    disp('MPC Controller Configuration:');
    mpcobj
catch ME
    % Display error message
    disp('Error creating MPC controller:');
    disp(ME.message);
    rethrow(ME);
end

%% 4. Run MPC Simulation
% Initialize history records
u_hist = zeros(time_step, length(input_cols));  % All input history
u_mpc_hist = zeros(time_step, nu);  % MPC-controlled input history only
y_hist = zeros(time_step, 1);  % Output history
x_hist = zeros(time_step, nx);  % State history
qp_status = zeros(time_step, 1);  % Optimization status record

% Initialize control input and state
u_prev = U_data(1, 1:nu)';
current_x = X_data(1, :)';

% Create and initialize MPC state object
state = mpcstate(mpcobj);

% Initialize state object
state.Plant = current_x;
state.LastMove = u_prev;


disp('Starting MPC simulation...');
tic;
u_opt_hist = zeros(time_step, PredictionHorizon+1);
y_opt_hist = zeros(time_step, PredictionHorizon+1);

for k = 1:time_step
    if k == 1
        x_current = X_data(1, :)';
        y_current = C * x_current;
    else
        % Calculate current state using real system model
        y_current = y_hist(k-1);
        x_current = x_hist(k-1, :)';
    end
    % Set reference trajectory
    ref = Y_ref(k:k+PredictionHorizon-1);
    % Set reference trajectory as column vector
    ref = ref(:);
    v = U_data(k, nu+1:end)';

    [u_opt, info] = mpcmove(mpcobj, state, y_current, ref, v);
    u_opt_hist(k,:) = info.Uopt';
    y_opt_hist(k,:) = info.Yopt';
    
    % Store QP status
    if isfield(info, 'QPStatus')
        qp_status(k) = info.QPStatus;
    end
    
    % Apply control input and build complete input vector
    u_full = U_data(k, :)';
    u_full(1:nu) = u_opt;
    
    % Simulate system response using state-space model
    x_next = A * state.Plant + B * u_full;
    y = C * x_next;
    
    % Record history
    u_hist(k, :) = u_full';
    u_mpc_hist(k, :) = u_opt';
    y_hist(k) = y;
    x_hist(k, :) = x_next';
    
    % Manually update next state, this is important as we use an external model
    state.Plant = x_next;
    
    % Debug output
    if mod(k, 10) == 0
        y_measured = C * X_data(k, :)';
        y_predicted = y;
        y_ref_k = Y_ref(k);
        
        % If optimization status is available, display it
        if isfield(info, 'QPStatus')
            status_str = '';
            switch info.QPStatus
                case 0
                    status_str = 'Success';
                case 1
                    status_str = 'Maximum iterations reached';
                case -1
                    status_str = 'No solution';
                otherwise
                    status_str = sprintf('Status code %d', info.QPStatus);
            end
            fprintf('Step %d: Reference=%.2f, Predicted=%.2f, Measured=%.2f, Optimization status=%s\n', ...
                k, y_ref_k, y_predicted, y_measured, status_str);
        else
            fprintf('Step %d: Reference=%.2f, Predicted=%.2f, Measured=%.2f\n', ...
                k, y_ref_k, y_predicted, y_measured);
        end
    end
end

sim_time = toc;
fprintf('MPC simulation completed in %.2f seconds\n', sim_time);

%% 5. Calculate Performance Metrics
% Calculate error
error = Y_ref(1:time_step) - y_hist;

% Calculate mean squared error and root mean squared error
mse = mean(error.^2);
rmse = sqrt(mse);
mae = mean(abs(error));

% Calculate tracking performance
avg_deviation = mean(abs(error));
max_deviation = max(abs(error));

% Output performance metrics
fprintf('\nPerformance Metrics:\n');
fprintf('Tracking Error - MSE: %.4f, RMSE: %.4f, MAE: %.4f\n', mse, rmse, mae);
fprintf('Average Deviation: %.2f°C\n', avg_deviation);
fprintf('Maximum Deviation: %.2f°C\n', max_deviation);

%% 6. Plot Results
% disp('Generating result figures...');
% 
% % Create figure
% fig = figure('Position', [50, 50, 1000, 800]);
% 
% % 1. Temperature comparison plot (MPC vs Reference vs Actual)
% subplot(3, 2, 1);
% plot(1:time_step, y_hist, 'b-', 'LineWidth', 2);
% hold on;
% plot(1:time_step, Y_ref(1:time_step), 'r--', 'LineWidth', 1.5);
% plot(1:time_step, Y_data(1:time_step), 'g-', 'LineWidth', 1.5);
% % Add upper and lower bound lines, reference temperature ±15 degrees
% plot(1:time_step, Y_ref(1:time_step) + 15, 'k--', 'LineWidth', 2); % Reference + 15
% plot(1:time_step, Y_ref(1:time_step) - 15, 'k--', 'LineWidth', 2); % Reference - 15
% hold off;
% title('Exit Temperature Control Performance');
% xlabel('Time Step');
% ylabel('Temperature (°C)');
% legend('MPC Control', 'Reference/Setpoint', 'Actual Measurement', 'Reference Bounds');
% grid on;
% 
% % 2. Error plot
% subplot(3, 2, 2);
% plot(1:time_step, Y_ref(1:time_step) - y_hist, 'b-', 'LineWidth', 2);
% hold on;
% plot(1:time_step, Y_ref(1:time_step) - Y_data(1:time_step), 'g-', 'LineWidth', 1);
% yline(0, 'k--');
% hold off;
% title('Control Error (Reference - Output)');
% xlabel('Time Step');
% ylabel('Error (°C)');
% legend('MPC Error', 'Actual Error');
% grid on;
% 
% % 3. Control input - Average power
% subplot(3, 2, 3);
% plot(1:time_step, u_hist(:, 1), 'b-', 'LineWidth', 2);
% hold on;
% plot(1:time_step, U_data(1:time_step, 1), 'r--', 'LineWidth', 1.5);
% hold off;
% title('Average Power Control');
% xlabel('Time Step');
% ylabel('Power (%)');
% legend('MPC Power Control', 'Original Power');
% grid on;
% 
% % 4. Speed
% subplot(3, 2, 4);
% hold on;
% plot(1:time_step, U_data(1:time_step, 2), 'r--', 'LineWidth', 1.5);
% hold off;
% title('Speed Control');
% xlabel('Time Step');
% ylabel('Speed');
% legend('Strip Speed');
% grid on;
% 
% % 5. Selected MPC predicted states (tube temperatures)
% subplot(3, 2, 5);
% tubes_to_plot = length(state_cols); % Plot first few tube temperatures
% for i = 1:tubes_to_plot
%     if i < length(state_cols) % Exclude exit temperature (last state)
%         plot(1:time_step, x_hist(:, i), 'DisplayName', state_cols{i}, 'LineWidth', 1.5);
%         hold on;
%     end
% end
% hold off;
% title('MPC Predicted States (Tube Temperatures)');
% xlabel('Time Step');
% ylabel('Temperature (°C)');
% grid on;
% 
% % 6. Control input rate of change
% subplot(3, 2, 6);
% plot(1:time_step-1, diff(u_hist(:, 1)), 'b-', 'LineWidth', 1.5);
% title('Control Rate of Change');
% xlabel('Time Step');
% ylabel('Rate of Change');
% legend('Power Rate of Change');
% grid on;
% 
% set(gcf, 'Color', 'w');
% 
% % Save figure
% if ~exist('fig', 'dir')
%     mkdir('fig');
% end
% file_name = sprintf('./paper_plot/MPC_matlab_P_ph_%d_ch_%d.png', PredictionHorizon, ControlHorizon);
% exportgraphics(fig, file_name, 'Resolution', 300)
%% 7. Plot Results (Two Figure)
disp('Generating result figures...');

% Create figure
fig = figure('Position', [50, 50, 1000, 300]);

% 1. Temperature comparison plot (MPC vs Reference vs Actual)
subplot(1, 2, 1);
plot(1:time_step, y_hist, 'b-', 'LineWidth', 2);
hold on;
plot(1:time_step, Y_ref(1:time_step), 'r--', 'LineWidth', 1.5);
plot(1:time_step, Y_data(1:time_step), 'g-', 'LineWidth', 1.5);
% Add upper and lower bound lines, reference temperature ±15 degrees
plot(1:time_step, Y_ref(1:time_step) + 15, 'k--', 'LineWidth', 2); % Reference + 15
plot(1:time_step, Y_ref(1:time_step) - 15, 'k--', 'LineWidth', 2); % Reference - 15
hold off;
title('Exit Temperature Control Performance');
xlabel('Time Step');
ylabel('Temperature (°C)');
legend('MPC Control', 'Reference/Setpoint', 'Actual Measurement', 'Reference Bounds');
grid on;
set(gca, 'LineWidth', 1.5);

% 2. Error plot
subplot(1, 2, 2);
plot(1:time_step, Y_ref(1:time_step) - y_hist, 'b-', 'LineWidth', 2);
hold on;
plot(1:time_step, Y_ref(1:time_step) - Y_data(1:time_step), 'g-', 'LineWidth', 1);
yline(0, 'k--');
hold off;
title('Control Error (Reference - Output)');
xlabel('Time Step');
ylabel('Error (°C)');
legend('MPC Error', 'Actual Error');
grid on;
set(gca, 'LineWidth', 1.5);
set(gcf, 'Color', 'w');


% Save figure
if ~exist('fig', 'dir')
    mkdir('fig');
end
file_name = sprintf('./paper_plot/MPC_matlab_P_ph_%d_ch_%d.png', PredictionHorizon, ControlHorizon);
exportgraphics(fig, file_name, 'Resolution', 300)
%% 8. Generate optimization process animation
make_gif = false;
if make_gif
    disp('Generating optimization process animation...');
    % Create a GIF file, set file path
    gif_filename = sprintf('./fig/optimization_process_P_ph_%d_ch_%d_start_%d_len_%d.gif', PredictionHorizon, ControlHorizon, start, time_step);

    % Set up figure window
    fig = figure();


    pastlen = 300;
    his_df = measures_df(start-pastlen:start-1, :);
    X_his_data = zeros(height(his_df), length(state_cols));
    for i = 1:length(state_cols)
        X_his_data(:, i) = his_df.(state_cols{i});
    end
    U_his_data = zeros(height(his_df), length(input_cols));
    for i = 1:length(input_cols)
        U_his_data(:, i) = his_df.(input_cols{i});
    end
    Y_his_data = his_df.measureExitTemp;
    Y_his_ref = his_df.setpointExitTemp;
    % Record frames for each optimization step
    for k = 1:time_step
        
        % Optimization data for current time step
        y_current = y_opt_hist(k,:);
        u_current = u_opt_hist(k,:);
        ref = Y_ref(k:k+PredictionHorizon);

        U_his_data = U_his_data(2:end, 1:2);
        U_his_data(end+1, :) = [u_current(1) U_data(k,2)];
        
        Y_his_data = Y_his_data(2:end);
        Y_his_data(end+1) = y_current(1);
        
        Y_his_ref = Y_his_ref(2:end);
        Y_his_ref(end+1) = ref(1,:);  
        
        % Plot upper part: strip exit temperature and reference temperature with bounds
        subplot(2, 1, 1);
        cla reset;
        hold on
        plot((1:pastlen)+k-1, Y_his_data(1:pastlen), 'b-', 'LineWidth', 2); % Historical data
        plot((1:pastlen)+k-1, Y_his_ref(1:pastlen), 'r--', 'LineWidth', 1.5); % Reference temperature
        plot((1:pastlen)+k-1, Y_his_ref(1:pastlen) + 15, 'k--', 'LineWidth', 2); % Upper bound +15
        plot((1:pastlen)+k-1, Y_his_ref(1:pastlen) - 15, 'k--', 'LineWidth', 2); % Lower bound -15
        xline(pastlen+k-1, '-', 'LineWidth', 2, 'Color', '#D95319', 'HandleVisibility', 'off'); 
        plot((pastlen:pastlen+PredictionHorizon)+k-1, y_current, 'b-', 'LineWidth', 2); % Future strip temperature
        plot((pastlen:pastlen+PredictionHorizon)+k-1, ref, 'r--', 'LineWidth', 2); % Future reference temperature
        plot((pastlen:pastlen+PredictionHorizon)+k-1, ref + 15, 'k--', 'LineWidth', 2); % Upper bound +15
        plot((pastlen:pastlen+PredictionHorizon)+k-1, ref - 15, 'k--', 'LineWidth', 2); % Lower bound -15
        hold off
        x_min = 1 + (k-1);
        x_max = pastlen + PredictionHorizon + (k-1);
        xlim([x_min, x_max]);
        ylim([min(ref) - 25 max(ref) + 25])
        xlabel('Time Step');
        ylabel('Temperature (°C)');
        title('Strip Exit Temperature and Reference Temperature');
        legend('Actual Temperature', 'Reference Temperature', 'Upper Bound', 'Lower Bound', 'Location','northwest');
        grid on;

        % Plot lower part: control inputs (power and speed)
        subplot(2, 1, 2);
        
        yyaxis left;
        cla;
        delete( findall(gca,'Type','ConstantLine') );
        hold on
        plot((1:pastlen)+k-1, U_his_data(1:pastlen, 1), 'b-', 'LineWidth', 2,'DisplayName','Historical Power'); % Power
        plot((pastlen:pastlen+PredictionHorizon)+k-1, u_current, 'b-', 'LineWidth', 2,'DisplayName','Future Power'); % Future power
        hold off
        ylabel('Power (%)');
        xlim([x_min, x_max]);
        ylim([0 100])
        xline(pastlen+k-1, '-', 'LineWidth', 2, 'Color', '#D95319', 'HandleVisibility', 'off');
        yyaxis right;
        cla;
        hold on
        plot((1:pastlen)+k-1, U_his_data(1:pastlen, 2), 'g-', 'LineWidth', 2,'DisplayName','Strip Speed'); % Speed
        plot((pastlen:pastlen+PredictionHorizon)+k-1, U_data(k:k+PredictionHorizon,2), 'g-', 'LineWidth', 2,'DisplayName','Future Strip Speed'); % Future speed
        hold off
        xlim([x_min, x_max]);
        ylabel('Speed');
        xlabel('Time Step');
        title('Control Inputs - Power and Speed');
        legend('Location','northwest');
        grid on;
    

        % Update figure
        drawnow;
        set(gcf, 'Color', 'w');

        % % Capture each frame
        % frame = getframe(fig);
        % im = frame2im(frame);
        % [A, map] = rgb2ind(im, 256);
        % 
        % % Save as GIF (write file on first iteration, append on subsequent ones)
        % if k == 1
        %     imwrite(A, map, gif_filename, 'gif', 'LoopCount', Inf, 'DelayTime', 0.01);
        % else
        %     imwrite(A, map, gif_filename, 'gif', 'WriteMode', 'append', 'DelayTime', 0.01);
        % end
        % First save each frame as PNG in the loop
        pngname = sprintf('gif/figs/frame_%03d.png', k);
        % exportgraphics(fig, pngname, 'Resolution',300);
        print(fig, pngname, '-dpng', '-r300');
        
        % Then convert to indexed image and write GIF
        img = imread(pngname);
        [A,map] = rgb2ind(img,256);
        if k==1
            imwrite(A, map, gif_filename, 'gif', 'LoopCount',Inf, 'DelayTime',0.01);
        else
            imwrite(A, map, gif_filename, 'gif', 'WriteMode','append', 'DelayTime',0.01);
        end
    end

    disp(['Optimization process animation generated, saved as: ', gif_filename]);
else
    disp('Animation not generated');
end
