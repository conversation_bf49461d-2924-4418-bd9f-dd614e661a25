%% MPC Validation Experiments with Custom Reference Trajectories
% This script validates the MPC controller with two designed experiments:
% 1. Temperature decrease: 800°C (500 steps) → 780°C (500 steps)
% 2. Temperature increase: 760°C (500 steps) → 780°C (500 steps)

clear;clc;close all

%% 1. Load State-Space Model
% Define model path
model_path = 'models/ss_ab_model_0.9'; % Update to your model path

% Load A matrix
A = csvread(fullfile(model_path, 'A_matrix.csv'));
% Load B matrix
B = csvread(fullfile(model_path, 'B_matrix.csv'));
% Load C matrix
C = csvread(fullfile(model_path, 'C_matrix.csv'));

% Load model information
fid = fopen(fullfile(model_path, 'model_info.json'), 'r');
raw = fread(fid, inf);
str = char(raw');
fclose(fid);
model_info = jsondecode(str);

% Extract variable information
state_cols = model_info.state_variables;
input_cols = model_info.input_variables;
output_col = model_info.output_variables{1};

disp(['Model loaded successfully, source: ', model_path]);
disp('Model structure:');
disp(['  - State variables: ', num2str(length(state_cols))]);
disp(['  - Input variables: ', num2str(length(input_cols))]);
disp(['  - Output variables: ', num2str(length(model_info.output_variables))]);

% Build discrete state-space model (already discrete)
Ts = 5; % Sampling time
plant = ss(A, B, C, zeros(size(C,1), size(B,2)), Ts);

%% 2. Load Data for Initial Conditions
% Define database filename and .mat filename
db_filename = './webApi/data/measure_20250601.sqlite';
[~, name, ~] = fileparts(db_filename);  % Get database filename (without extension)
mat_filename = ['./data/' name '.mat'];  % Path and filename for saved data (.mat format)

% Check if file already exists
if exist(mat_filename, 'file')
    % If file exists, load the data
    disp('Loading existing measures_df data...');
    load(mat_filename, 'measures_df');
else
    error('Data file not found. Please run MPC_matlab_control_P.m first to generate the data file.');
end

%% 3. Design Reference Trajectories for Two Experiments

% Total simulation time
total_steps = 2000;

% Initialize reference trajectory
Y_ref = zeros(total_steps, 1);

% Experiment 1: Temperature decrease (steps 1-1000)
% 800°C for 500 steps, then 780°C for 500 steps
Y_ref(1:500) = 800;
Y_ref(501:1000) = 780;

% Experiment 2: Temperature increase (steps 1001-2000)  
% 760°C for 500 steps, then 780°C for 500 steps
Y_ref(1001:1500) = 760;
Y_ref(1501:2000) = 780;

%% 4. Design Temperature Bounds with Smooth Transitions
% Initialize upper and lower bounds
Y_upper = zeros(total_steps, 1);
Y_lower = zeros(total_steps, 1);

% For temperature decrease (Experiment 1: steps 1-1000)
% Keep lower bound consistent for smooth transition
lower_bound_exp1 = min(Y_ref(1:1000)) - 10; % 780 - 10 = 770°C
Y_lower(1:1000) = lower_bound_exp1;
Y_upper(1:500) = Y_ref(1:500) + 10;    % 800 + 10 = 810°C
Y_upper(501:1000) = Y_ref(501:1000) + 10; % 780 + 10 = 790°C

% For temperature increase (Experiment 2: steps 1001-2000)
% Keep upper bound consistent for smooth transition  
upper_bound_exp2 = max(Y_ref(1001:2000)) + 10; % 780 + 10 = 790°C
Y_upper(1001:2000) = upper_bound_exp2;
Y_lower(1001:1500) = Y_ref(1001:1500) - 10; % 760 - 10 = 750°C
Y_lower(1501:2000) = Y_ref(1501:2000) - 10; % 780 - 10 = 770°C

%% 5. Prepare MPC Controller
PredictionHorizon = 60;
ControlHorizon = 30;

% Use initial conditions from real data
start_idx = 1;
initial_data = measures_df(start_idx:start_idx+100, :);

% Extract initial state, input data
X_initial = zeros(1, length(state_cols));
for i = 1:length(state_cols)
    X_initial(i) = initial_data.(state_cols{i})(1);
end

U_initial = zeros(1, length(input_cols));
for i = 1:length(input_cols)
    U_initial(i) = initial_data.(input_cols{i})(1);
end

% Use typical values for disturbance inputs (speed, thickness, etc.)
% These will be kept constant during the experiments
U_disturbance = U_initial(2:end); % All inputs except power (first input)

% Control constraints
u_min = 0;  % Minimum power = 0%
u_max = 100;  % Maximum power = 100%

du_min = -10;  % Minimum control rate of change
du_max = 10;   % Maximum control rate of change

% Output constraints
y_min = min(Y_lower);  
y_max = max(Y_upper);  

% Build MATLAB MPC controller
nu = 1;   % Number of control inputs (power)
ny = 1;   % Number of outputs (exit temperature)
nx = size(A, 1);  % Number of states
nd = size(B, 2) - nu;  % Number of measured disturbances

% Create prediction model with measured disturbances
Bu = B(:, 1:nu);
Bd = B(:, nu+1:end);

% Set MPC weights
W = struct();
W.ManipulatedVariables = 0.01 * ones(1,nu);      % Weight for control inputs
W.ManipulatedVariablesRate = 1 * ones(1,nu);     % Weight for control input rate of change
W.OutputVariables = 100;                         % Weight for output variables

% Create MPC constraints
MV = struct('Min', u_min, 'Max', u_max, 'RateMin', du_min, 'RateMax', du_max);
OV = struct('Min', y_min, 'Max', y_max);

try
    % Clear potentially existing old MPC objects
    clear mpcobj
    
    % Create new state-space model including disturbances
    plant_mpc = ss(A, [Bu Bd], C, zeros(ny, nu+nd), Ts);
    
    % Set MPC signal types
    plant_mpc = setmpcsignals(plant_mpc, 'MV', 1:nu, 'MD', nu+1:nu+nd);
    
    % Create MPC object
    mpcobj = mpc(plant_mpc, Ts, PredictionHorizon, ControlHorizon, W, MV, OV);
        
    % Display MPC object information
    disp('MPC Controller Configuration:');
    mpcobj
catch ME
    % Display error message
    disp('Error creating MPC controller:');
    disp(ME.message);
    rethrow(ME);
end

%% 6. Run MPC Simulation
% Initialize history records
u_hist = zeros(total_steps, nu);  % Control input history (power only)
y_hist = zeros(total_steps, 1);   % Output history
x_hist = zeros(total_steps, nx);  % State history
qp_status = zeros(total_steps, 1); % Optimization status record

% Initialize control input and state
u_prev = U_initial(1);  % Initial power
current_x = X_initial'; % Initial state

% Create and initialize MPC state object
state = mpcstate(mpcobj);

% Initialize state object
state.Plant = current_x;
state.LastMove = u_prev;

disp('Starting MPC validation experiments...');
tic;

for k = 1:total_steps
    if k == 1
        x_current = current_x;
        y_current = C * x_current;
    else
        % Use previous simulation results
        y_current = y_hist(k-1);
        x_current = x_hist(k-1, :)';
    end

    % Set reference trajectory (extend for prediction horizon)
    if k + PredictionHorizon - 1 <= total_steps
        ref = Y_ref(k:k+PredictionHorizon-1);
    else
        % Extend with last reference value
        ref = [Y_ref(k:end); repmat(Y_ref(end), k+PredictionHorizon-1-total_steps, 1)];
    end
    ref = ref(:); % Ensure column vector

    % Set disturbance inputs (constant throughout simulation)
    v = repmat(U_disturbance', 1, 1);

    % Solve MPC optimization
    [u_opt, info] = mpcmove(mpcobj, state, y_current, ref, v);

    % Store QP status
    if isfield(info, 'QPStatus')
        qp_status(k) = info.QPStatus;
    end

    % Build complete input vector for simulation
    u_full = [u_opt; U_disturbance'];

    % Simulate system response using state-space model
    x_next = A * state.Plant + B * u_full;
    y = C * x_next;

    % Record history
    u_hist(k) = u_opt;
    y_hist(k) = y;
    x_hist(k, :) = x_next';

    % Update state for next iteration
    state.Plant = x_next;

    % Debug output every 100 steps
    if mod(k, 100) == 0
        y_ref_k = Y_ref(k);

        % Display optimization status if available
        if isfield(info, 'QPStatus')
            status_str = '';
            switch info.QPStatus
                case 0
                    status_str = 'Success';
                case 1
                    status_str = 'Maximum iterations reached';
                case -1
                    status_str = 'No solution';
                otherwise
                    status_str = sprintf('Status code %d', info.QPStatus);
            end
            fprintf('Step %d: Reference=%.2f, Predicted=%.2f, Power=%.2f%%, Status=%s\n', ...
                k, y_ref_k, y, u_opt, status_str);
        else
            fprintf('Step %d: Reference=%.2f, Predicted=%.2f, Power=%.2f%%\n', ...
                k, y_ref_k, y, u_opt);
        end
    end
end

sim_time = toc;
fprintf('MPC validation experiments completed in %.2f seconds\n', sim_time);

%% 7. Calculate Performance Metrics for Both Experiments

% Experiment 1 (steps 1-1000): Temperature decrease
exp1_indices = 1:1000;
error_exp1 = Y_ref(exp1_indices) - y_hist(exp1_indices);
mse_exp1 = mean(error_exp1.^2);
rmse_exp1 = sqrt(mse_exp1);
mae_exp1 = mean(abs(error_exp1));
max_dev_exp1 = max(abs(error_exp1));

% Experiment 2 (steps 1001-2000): Temperature increase
exp2_indices = 1001:2000;
error_exp2 = Y_ref(exp2_indices) - y_hist(exp2_indices);
mse_exp2 = mean(error_exp2.^2);
rmse_exp2 = sqrt(mse_exp2);
mae_exp2 = mean(abs(error_exp2));
max_dev_exp2 = max(abs(error_exp2));

% Overall performance
error_total = Y_ref - y_hist;
mse_total = mean(error_total.^2);
rmse_total = sqrt(mse_total);
mae_total = mean(abs(error_total));
max_dev_total = max(abs(error_total));

% Output performance metrics
fprintf('\nPerformance Metrics:\n');
fprintf('===================\n');
fprintf('Experiment 1 (Temperature Decrease 800°C→780°C):\n');
fprintf('  MSE: %.4f, RMSE: %.4f, MAE: %.4f\n', mse_exp1, rmse_exp1, mae_exp1);
fprintf('  Maximum Deviation: %.2f°C\n', max_dev_exp1);
fprintf('\nExperiment 2 (Temperature Increase 760°C→780°C):\n');
fprintf('  MSE: %.4f, RMSE: %.4f, MAE: %.4f\n', mse_exp2, rmse_exp2, mae_exp2);
fprintf('  Maximum Deviation: %.2f°C\n', max_dev_exp2);
fprintf('\nOverall Performance:\n');
fprintf('  MSE: %.4f, RMSE: %.4f, MAE: %.4f\n', mse_total, rmse_total, mae_total);
fprintf('  Maximum Deviation: %.2f°C\n', max_dev_total);

%% 8. Plot Results Following Original Format
disp('Generating validation experiment figures...');

% Create figure with same format as original
fig = figure('Position', [50, 50, 1000, 600]);

% Plot 1: Complete temperature trajectory with bounds
subplot(2, 2, 1);
plot(1:total_steps, y_hist, 'b-', 'LineWidth', 2);
hold on;
plot(1:total_steps, Y_ref, 'r--', 'LineWidth', 1.5);
plot(1:total_steps, Y_upper, 'k--', 'LineWidth', 2);
plot(1:total_steps, Y_lower, 'k--', 'LineWidth', 2);
% Add vertical line to separate experiments
xline(1000, 'g-', 'LineWidth', 2, 'Alpha', 0.7);
hold off;
title('MPC Temperature Control - Both Experiments');
xlabel('Time Step');
ylabel('Temperature (°C)');
legend('MPC Control', 'Reference', 'Upper Bound', 'Lower Bound', 'Exp Boundary', 'Location', 'best');
grid on;
set(gca, 'LineWidth', 1.5);

% Plot 2: Experiment 1 - Temperature decrease
subplot(2, 2, 2);
plot(exp1_indices, y_hist(exp1_indices), 'b-', 'LineWidth', 2);
hold on;
plot(exp1_indices, Y_ref(exp1_indices), 'r--', 'LineWidth', 1.5);
plot(exp1_indices, Y_upper(exp1_indices), 'k--', 'LineWidth', 2);
plot(exp1_indices, Y_lower(exp1_indices), 'k--', 'LineWidth', 2);
% Add vertical line at step 500
xline(500, 'g-', 'LineWidth', 1.5, 'Alpha', 0.7);
hold off;
title('Experiment 1: Temperature Decrease (800°C→780°C)');
xlabel('Time Step');
ylabel('Temperature (°C)');
legend('MPC Control', 'Reference', 'Upper Bound', 'Lower Bound', 'Setpoint Change', 'Location', 'best');
grid on;
set(gca, 'LineWidth', 1.5);

% Plot 3: Experiment 2 - Temperature increase
subplot(2, 2, 3);
plot(exp2_indices, y_hist(exp2_indices), 'b-', 'LineWidth', 2);
hold on;
plot(exp2_indices, Y_ref(exp2_indices), 'r--', 'LineWidth', 1.5);
plot(exp2_indices, Y_upper(exp2_indices), 'k--', 'LineWidth', 2);
plot(exp2_indices, Y_lower(exp2_indices), 'k--', 'LineWidth', 2);
% Add vertical line at step 1500
xline(1500, 'g-', 'LineWidth', 1.5, 'Alpha', 0.7);
hold off;
title('Experiment 2: Temperature Increase (760°C→780°C)');
xlabel('Time Step');
ylabel('Temperature (°C)');
legend('MPC Control', 'Reference', 'Upper Bound', 'Lower Bound', 'Setpoint Change', 'Location', 'best');
grid on;
set(gca, 'LineWidth', 1.5);

% Plot 4: Control input (power) for both experiments
subplot(2, 2, 4);
plot(1:total_steps, u_hist, 'b-', 'LineWidth', 2);
hold on;
% Add vertical lines to show experiment boundaries
xline(500, 'g-', 'LineWidth', 1.5, 'Alpha', 0.7);
xline(1000, 'r-', 'LineWidth', 2, 'Alpha', 0.7);
xline(1500, 'g-', 'LineWidth', 1.5, 'Alpha', 0.7);
hold off;
title('MPC Control Input - Power (%)');
xlabel('Time Step');
ylabel('Power (%)');
legend('Power Control', 'Setpoint Changes', 'Exp Boundary', 'Location', 'best');
grid on;
set(gca, 'LineWidth', 1.5);

set(gcf, 'Color', 'w');

% Save figure
if ~exist('fig', 'dir')
    mkdir('fig');
end
file_name = sprintf('./fig/MPC_validation_experiments_ph_%d_ch_%d.png', PredictionHorizon, ControlHorizon);
exportgraphics(fig, file_name, 'Resolution', 300);

%% 9. Create Detailed Error Analysis Plot
fig2 = figure('Position', [100, 100, 1000, 400]);

% Plot 1: Control errors for both experiments
subplot(1, 2, 1);
plot(1:total_steps, error_total, 'b-', 'LineWidth', 2);
hold on;
yline(0, 'k--', 'LineWidth', 1);
xline(1000, 'r-', 'LineWidth', 2, 'Alpha', 0.7);
hold off;
title('Control Error (Reference - Output)');
xlabel('Time Step');
ylabel('Error (°C)');
legend('Control Error', 'Zero Error', 'Exp Boundary', 'Location', 'best');
grid on;
set(gca, 'LineWidth', 1.5);

% Plot 2: Control input rate of change
subplot(1, 2, 2);
plot(1:total_steps-1, diff(u_hist), 'b-', 'LineWidth', 1.5);
hold on;
yline(du_max, 'r--', 'LineWidth', 1.5);
yline(du_min, 'r--', 'LineWidth', 1.5);
xline(1000, 'g-', 'LineWidth', 2, 'Alpha', 0.7);
hold off;
title('Control Rate of Change');
xlabel('Time Step');
ylabel('Power Rate of Change (%/step)');
legend('Power Rate', 'Rate Limits', 'Exp Boundary', 'Location', 'best');
grid on;
set(gca, 'LineWidth', 1.5);

set(gcf, 'Color', 'w');

% Save error analysis figure
file_name2 = sprintf('./fig/MPC_validation_error_analysis_ph_%d_ch_%d.png', PredictionHorizon, ControlHorizon);
exportgraphics(fig2, file_name2, 'Resolution', 300);

fprintf('\nValidation experiment figures saved:\n');
fprintf('  Main results: %s\n', file_name);
fprintf('  Error analysis: %s\n', file_name2);

disp('MPC validation experiments completed successfully!');
