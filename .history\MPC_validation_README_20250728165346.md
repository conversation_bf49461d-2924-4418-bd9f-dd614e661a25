# MPC验证实验说明

## 概述
`MPC_validation_experiments.m` 是一个专门设计的MATLAB脚本，用于验证MPC控制器的性能。该脚本包含两个精心设计的实验，测试控制器在不同温度变化场景下的表现。

## 实验设计

### 实验1：温度下降测试
- **时间范围**: 第1-1000步
- **温度设定**: 
  - 第1-500步: 800°C (保持500步)
  - 第501-1000步: 780°C (保持500步)
- **目的**: 测试控制器处理温度下降的能力

### 实验2：温度上升测试
- **时间范围**: 第1001-2000步
- **温度设定**:
  - 第1001-1500步: 770°C (保持500步)
  - 第1501-2000步: 790°C (保持500步)
- **目的**: 测试控制器处理温度上升的能力

## 温度边界设计

### 平滑过渡策略
为了保证过渡的平滑性，温度边界设计遵循以下原则：

#### 实验1 (温度下降)
- **下限温度**: 保持一致为770°C (780-10)
- **上限温度**: 
  - 第1-500步: 810°C (800+10)
  - 第501-1000步: 790°C (780+10)

#### 实验2 (温度上升)
- **上限温度**: 保持一致为790°C (780+10)
- **下限温度**:
  - 第1001-1500步: 750°C (760-10)
  - 第1501-2000步: 770°C (780-10)

## 绘图输出

### 主要结果图 (`MPC_validation_experiments_ph_60_ch_30.png`)
包含4个子图：
1. **完整温度轨迹**: 显示整个2000步的控制效果
2. **实验1详细图**: 温度下降过程的详细分析
3. **实验2详细图**: 温度上升过程的详细分析  
4. **功率控制输入**: 显示MPC的功率控制策略

### 误差分析图 (`MPC_validation_error_analysis_ph_60_ch_30.png`)
包含2个子图：
1. **控制误差**: 参考温度与实际输出的差值
2. **控制变化率**: 功率控制的变化速率及其约束

## 性能指标

脚本会自动计算并显示以下性能指标：

### 实验1 (温度下降)
- MSE (均方误差)
- RMSE (均方根误差)  
- MAE (平均绝对误差)
- 最大偏差

### 实验2 (温度上升)
- MSE (均方误差)
- RMSE (均方根误差)
- MAE (平均绝对误差)
- 最大偏差

### 总体性能
- 整体MSE、RMSE、MAE
- 最大偏差

## 使用方法

1. 确保已运行 `MPC_matlab_control_P.m` 生成必要的数据文件
2. 确保模型文件存在于 `models/ss_ab_model_0.9/` 目录
3. 运行脚本：
   ```matlab
   run('MPC_validation_experiments.m')
   ```

## 输出文件

- `./fig/MPC_validation_experiments_ph_60_ch_30.png`: 主要结果图
- `./fig/MPC_validation_error_analysis_ph_60_ch_30.png`: 误差分析图

## 特点

1. **参考原始格式**: 绘图风格与 `MPC_matlab_control_P.m` 保持一致
2. **平滑边界设计**: 确保温度边界的平滑过渡
3. **详细性能分析**: 提供全面的控制性能评估
4. **清晰的可视化**: 多角度展示控制效果
5. **自动化处理**: 一键运行完成所有分析和绘图
